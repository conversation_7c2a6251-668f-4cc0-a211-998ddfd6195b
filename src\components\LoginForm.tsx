import React, { useState } from 'react';
import api from '../api/axios';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const login = async (e: React.FormEvent) => {
    e.preventDefault();
    await api.get('/sanctum/csrf-cookie');
    await api.post('/login', { email, password });
    alert('Logged in successfully!');
  };

  return (
    <form onSubmit={login} className="space-y-4">
      <input type="email" required value={email} onChange={e => setEmail(e.target.value)} className="w-full border p-2" placeholder="Email" />
      <input type="password" required value={password} onChange={e => setPassword(e.target.value)} className="w-full border p-2" placeholder="Password" />
      <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded">Login</button>
    </form>
  );
};

export default LoginForm;
