# Graduate Tracker Backend 🔧

Oy, backend ni sa graduate tracker. PHP ra ni, simple server lang for now. <PERSON><PERSON> supposed to be pero naa issues sa composer, so ginawa nako simple PHP server na lang.

## Unsa naa diri?

- Simple PHP server (temporary)
- API endpoints for authentication
- CORS setup for frontend
- Session-based auth (simple lang)

## Paano mo run?

### Super simple lang:
```bash
cd src/graduate-tracker-backend
php -S localhost:8000 simple-server.php
```

Ato na! Mag run na ni sa `http://localhost:8000`

## API Endpoints

### Authentication
- `GET /` - API status check
- `GET /sanctum/csrf-cookie` - CSRF token (fake lang)
- `POST /register` - User registration
- `POST /login` - User login
- `GET /api/user` - Get current user
- `POST /logout` - Logout

### Sample requests:

**Register:**
```bash
curl -X POST http://localhost:8000/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'
```

**Login:**
```bash
curl -X POST http://localhost:8000/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## Laravel Setup (if gusto mo i-fix)

Naa pa ang Laravel files diri pero dili mo gana tungod sa composer issues. If gusto mo i-fix:

```bash
composer install
php artisan key:generate
php artisan migrate
php artisan serve
```

Pero for now, simple-server.php ra ang ga work.

## Database

Naa SQLite file sa `database/database.sqlite` pero wala pa ga gamit. Session-based ra ang auth for now.

## CORS

Naka setup na para sa `http://localhost:3001` (frontend). If lain ang port sa frontend, change lang sa `simple-server.php`.

## Admin Access

If naa "admin" sa email (like <EMAIL>), ma set as admin role. Simple logic ra.

## Issues?

Check ang PHP error logs. Naa debugging sa `simple-server.php`. If dili gana, restart lang ang server.

Ato na! 🚀
