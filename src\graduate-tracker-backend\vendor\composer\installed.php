<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '9044fbae3ea5e67449082b6f42b5f3380f1ddee0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '9044fbae3ea5e67449082b6f42b5f3380f1ddee0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.1',
            'version' => '0.12.1.0',
            'reference' => 'f510c0a40911935b77b86859eb5223d58d660df1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'ebaaf5be6c0286928352e054f2d5125608e5405e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '9b9f7b4aefb4773772a827f9562336805926fe63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'a01a9d0799700bf34ab3797988fdd5f420d42bfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '362dd761b9920367bca1427a902158225e9e3a23',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'c4c9e69e240601cca841ec327e8e804deb6b965e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '6e31eb49e9c9e68356a55cd8f18fb8830b8158cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '44c15aec6ea0d997e0885aa5b04876fe8a141433',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'af826028f0a4e65a331ceb9bebfa6a71467ba72c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'ce7013a350fb06bc65e8a2cf15fd2015f49e476d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'b359be74adc3ba4a637ca01c3645a26724a4c8a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '213bc04ed2a75dac441e602df4568154c36a3670',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/translation' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => '8ef07f0148bf48e4f342efdcd39869c24cb7c81f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/validation' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'ac7cfbf48fdafc3b0a6ca13f0547d2bec83261ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/validation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v11.32.0',
            'version' => '*********',
            'reference' => 'b0ca05925b882b7887f9c2591497a1b2835f9144',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.2',
            'version' => '0.3.2.0',
            'reference' => '0e0535747c6b8d6d10adca8b68293cf4517abb0f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v4.5.2',
            'version' => '4.5.2.0',
            'reference' => 'c75396f63268c95b053c8e4814eb70e0875e9628',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.8.2',
            'version' => '3.8.2.0',
            'reference' => 'e1268cdbc486d97ce23fef2c666dc3c6b6de9947',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => '42c84e4e8090766bbd6445d06cd6e57650626ea3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.1.6',
            'version' => '7.1.6.0',
            'reference' => '97bebc53548684c17ed696bc8af016880f0f098d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.1.8',
            'version' => '7.1.8.0',
            'reference' => 'ff04e5b5ba043d2badfb308197b9e6b42883fcd5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.1.6',
            'version' => '7.1.6.0',
            'reference' => '2cb89664897be33f78c65d3d2845954c8d7a43b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.1.8',
            'version' => '7.1.8.0',
            'reference' => 'f4419ec69ccfc3f725a4de7c20e4e57626d10112',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.1.6',
            'version' => '7.1.6.0',
            'reference' => 'caa1e521edb2650b8470918dfe51708c237f0598',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.1.8',
            'version' => '7.1.8.0',
            'reference' => '42783370fda6e538771f7c7a36e9fa2ee3a84892',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'bd1d9e59a81d8fa4acdcea3f617c581f7475a80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.1.8',
            'version' => '7.1.8.0',
            'reference' => '591ebd41565f356fcd8b090fe64dbb5878f50281',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.1.6',
            'version' => '7.1.6.0',
            'reference' => 'b9f72ab14efdb6b772f85041fa12f820dee8d55f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'b9d2189887bb6b2e0367a9fc7136c5239ab9b05a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
