# Graduate Tracker Project 🎓📊

Oy, complete graduate tracker application ni. Frontend ug backend naa na diri. Simple ra pero functional na.

## Project Structure

```
graduate-tracker-frontend/
├── README.md                    # Frontend README
├── src/                        # React frontend
│   ├── components/             # UI components
│   ├── pages/                  # Page components
│   ├── api/                    # API config
│   └── graduate-tracker-backend/  # Backend folder
│       ├── README.md           # Backend README
│       ├── simple-server.php   # PHP server
│       └── app/                # Laravel files (broken pa)
└── PROJECT-README.md           # This file
```

## Quick Start (Both Services)

### 1. Frontend (React)
```bash
npm install
npm start
```
Mag run sa `http://localhost:3001`

### 2. Backend (PHP)
```bash
cd src/graduate-tracker-backend
php -S localhost:8000 simple-server.php
```
Mag run sa `http://localhost:8000`

## Features

### Authentication
- User registration
- User login
- Admin login (if "admin" naa sa email)
- Session-based auth

### UI Components
- Landing page with login/register
- Admin login page
- Responsive design with Tailwind CSS
- Blue theme for users, gray for admin

## Tech Stack

### Frontend
- React 19 + TypeScript
- Tailwind CSS
- React Router
- Axios

### Backend
- PHP (simple server)
- Session-based auth
- CORS enabled
- Laravel files (pero broken pa ang composer)

## Development

Both services naka setup na with hot reload. Change lang ang code, auto refresh na.

### Debugging
- Frontend: Check browser console
- Backend: Check terminal output

### API Testing
```bash
# Test registration
curl -X POST http://localhost:8000/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","password":"123"}'

# Test login
curl -X POST http://localhost:8000/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123"}'
```

## Known Issues

1. Laravel backend broken (composer permission issues)
2. Using simple PHP server temporarily
3. No database yet (session-based lang)
4. Basic validation lang

## Next Steps

1. Fix Laravel backend
2. Add proper database
3. Add more features (dashboard, user management)
4. Add proper validation
5. Add tests

## Deployment

For now, development lang. For production:
- Frontend: `npm run build`
- Backend: Fix Laravel first

Ato na! 🚀
