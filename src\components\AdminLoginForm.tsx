import React, { useState } from 'react';
import api from '../api/axios';

const AdminLoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const login = async (e: React.FormEvent) => {
    e.preventDefault();
    await api.get('/sanctum/csrf-cookie');
    const response = await api.post('/login', { email, password });

    // You can also fetch `/api/user` here to verify role
    const { data } = await api.get('/api/user');
    if (data.role !== 'admin') {
      alert("Not authorized as admin!");
      return;
    }

    alert("Admin logged in!");
    // redirect to admin dashboard here if needed
  };

  return (
    <form onSubmit={login} className="space-y-4">
      <input type="email" value={email} onChange={e => setEmail(e.target.value)} className="w-full border p-2" placeholder="Admin Email" />
      <input type="password" value={password} onChange={e => setPassword(e.target.value)} className="w-full border p-2" placeholder="Password" />
      <button type="submit" className="w-full bg-red-600 text-white py-2 rounded">Admin Login</button>
    </form>
  );
};

export default AdminLoginForm;
