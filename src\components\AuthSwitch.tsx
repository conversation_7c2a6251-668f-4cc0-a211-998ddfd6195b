import React, { useState } from "react";
import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm"

const AuthSwitch = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-xl shadow-md">
      <div className="flex mb-4 border-b">
        <button
          className={`flex-1 py-2 text-center ${isLogin ? "border-b-2 border-blue-600 font-bold" : "text-gray-400"}`}
          onClick={() => setIsLogin(true)}
        >
          Login
        </button>
        <button
          className={`flex-1 py-2 text-center ${!isLogin ? "border-b-2 border-blue-600 font-bold" : "text-gray-400"}`}
          onClick={() => setIsLogin(false)}
        >
          Register
        </button>
      </div>
      {isLogin ? <LoginForm /> : <RegisterForm />}
    </div>
  );
};

export default AuthSwitch;
