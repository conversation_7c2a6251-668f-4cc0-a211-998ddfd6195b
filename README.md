# Graduate Tracker Frontend 🎓

Oy, this is the frontend part sa graduate tracker app. React ra ni with TypeScript, naa pay Tailwind for styling. Dili complicated, simple ra.

## mga features

- React 19 with TypeScript (latest na)
- Tailwind CSS (para nindot ang styling)
- React Router (para sa navigation)
- Axios (para sa API calls)

## para pa run

### quick start lang:
```bash
npm install    # install dependencies first
npm start      # then run
```

Mag open ni sa browser, usually `http://localhost:3000` or `http://localhost:3001` if naa nay naka occupy sa 3000.

### pwede raka mo try ug other prompt para run 
```bash
npm test       # run tests (if naa)
npm run build  # build for production
```



## paras api nga setup

Backend dapat naka run sa `http://localhost:8000`. Check ang `src/api/axios.ts` if need mo change ang URL.

## noteee!!!! 

Check console sa browser. Naa debugging logs sa API calls. If dili pa gana, restart lang both frontend ug backend.

