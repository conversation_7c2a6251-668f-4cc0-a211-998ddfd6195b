import React, { useState } from 'react';
import api from '../api/axios';

const RegisterForm = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const register = async (e: React.FormEvent) => {
    e.preventDefault();
    await api.get('/sanctum/csrf-cookie');
    await api.post('/register', {
      name,
      email,
      password,
      password_confirmation: password
    });
    alert('Registered successfully!');
  };

  return (
    <form onSubmit={register} className="space-y-4">
      <input type="text" required value={name} onChange={e => setName(e.target.value)} className="w-full border p-2" placeholder="Name" />
      <input type="email" required value={email} onChange={e => setEmail(e.target.value)} className="w-full border p-2" placeholder="Email" />
      <input type="password" required value={password} onChange={e => setPassword(e.target.value)} className="w-full border p-2" placeholder="Password" />
      <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded">Register</button>
    </form>
  );
};

export default RegisterForm;
