<?php
// Simple PHP server to handle API requests for testing
header('Access-Control-Allow-Origin: http://localhost:3001');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Log all requests for debugging
error_log("Request: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple in-memory storage (for testing only)
session_start();

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove any leading path components to get just the endpoint
$path = preg_replace('/^.*\/simple-server\.php/', '', $path);
if (empty($path)) {
    $path = '/';
}

// Log the parsed path for debugging
error_log("Parsed path: " . $path);

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

switch ($path) {
    case '/':
        echo json_encode(['message' => 'Graduate Tracker API', 'Laravel' => 'Simple PHP Server']);
        break;
        
    case '/sanctum/csrf-cookie':
        // Simulate CSRF cookie
        setcookie('XSRF-TOKEN', 'test-csrf-token', time() + 3600, '/', '', false, false);
        http_response_code(204);
        break;

    case '/register':
        if ($method === 'POST') {
            $name = $input['name'] ?? '';
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($name) || empty($email) || empty($password)) {
                http_response_code(422);
                echo json_encode(['errors' => ['All fields are required']]);
                break;
            }

            // Initialize registered users array if not exists
            if (!isset($_SESSION['registered_users'])) {
                $_SESSION['registered_users'] = [];
            }

            // Check if email already exists
            foreach ($_SESSION['registered_users'] as $user) {
                if ($user['email'] === $email) {
                    http_response_code(422);
                    echo json_encode(['errors' => ['Email already exists']]);
                    break 2; // Break out of both foreach and case
                }
            }

            // Determine role based on email
            $role = (strpos($email, 'admin') !== false) ? 'admin' : 'user';

            // Store user in session
            $newUser = [
                'id' => count($_SESSION['registered_users']) + 1,
                'name' => $name,
                'email' => $email,
                'password' => $password, // In real app, this should be hashed!
                'role' => $role
            ];

            $_SESSION['registered_users'][] = $newUser;

            // Auto-login the user after registration
            $_SESSION['user'] = [
                'id' => $newUser['id'],
                'name' => $newUser['name'],
                'email' => $newUser['email'],
                'role' => $newUser['role']
            ];

            http_response_code(204);
        }
        break;

    case '/login':
        if ($method === 'POST') {
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($email) || empty($password)) {
                http_response_code(422);
                echo json_encode(['errors' => ['Email and password are required']]);
                break;
            }

            // Check if user exists in session storage (from registration)
            $registeredUsers = $_SESSION['registered_users'] ?? [];
            $userFound = false;
            $userData = null;

            foreach ($registeredUsers as $user) {
                if ($user['email'] === $email && $user['password'] === $password) {
                    $userFound = true;
                    $userData = $user;
                    break;
                }
            }

            if (!$userFound) {
                http_response_code(401);
                echo json_encode(['errors' => ['Invalid email or password']]);
                break;
            }

            // Set logged in user
            $_SESSION['user'] = [
                'id' => $userData['id'],
                'name' => $userData['name'],
                'email' => $userData['email'],
                'role' => $userData['role']
            ];

            http_response_code(204);
        }
        break;
        
    case '/api/user':
        if ($method === 'GET') {
            if (isset($_SESSION['user'])) {
                echo json_encode($_SESSION['user']);
            } else {
                http_response_code(401);
                echo json_encode(['message' => 'Unauthenticated']);
            }
        }
        break;
        
    case '/logout':
        if ($method === 'POST') {
            session_destroy();
            http_response_code(204);
        }
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['message' => 'Not found']);
        break;
}
?>
