<?php
// Simple PHP server to handle API requests for testing
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple in-memory storage (for testing only)
session_start();

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove any leading path components to get just the endpoint
$path = preg_replace('/^.*\/simple-server\.php/', '', $path);
if (empty($path)) {
    $path = '/';
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

switch ($path) {
    case '/':
        echo json_encode(['message' => 'Graduate Tracker API', 'Laravel' => 'Simple PHP Server']);
        break;
        
    case '/sanctum/csrf-cookie':
        // Simulate CSRF cookie
        setcookie('XSRF-TOKEN', 'test-csrf-token', time() + 3600, '/', '', false, false);
        http_response_code(204);
        break;
        
    case '/register':
        if ($method === 'POST') {
            $name = $input['name'] ?? '';
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';
            
            if (empty($name) || empty($email) || empty($password)) {
                http_response_code(422);
                echo json_encode(['errors' => ['All fields are required']]);
                break;
            }
            
            // Simulate user registration
            $_SESSION['user'] = [
                'id' => 1,
                'name' => $name,
                'email' => $email,
                'role' => 'user'
            ];
            
            http_response_code(204);
        }
        break;
        
    case '/login':
        if ($method === 'POST') {
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';
            
            if (empty($email) || empty($password)) {
                http_response_code(422);
                echo json_encode(['errors' => ['Email and password are required']]);
                break;
            }
            
            // Simulate login (accept any email/password for testing)
            $role = (strpos($email, 'admin') !== false) ? 'admin' : 'user';
            
            $_SESSION['user'] = [
                'id' => 1,
                'name' => 'Test User',
                'email' => $email,
                'role' => $role
            ];
            
            http_response_code(204);
        }
        break;
        
    case '/api/user':
        if ($method === 'GET') {
            if (isset($_SESSION['user'])) {
                echo json_encode($_SESSION['user']);
            } else {
                http_response_code(401);
                echo json_encode(['message' => 'Unauthenticated']);
            }
        }
        break;
        
    case '/logout':
        if ($method === 'POST') {
            session_destroy();
            http_response_code(204);
        }
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['message' => 'Not found']);
        break;
}
?>
