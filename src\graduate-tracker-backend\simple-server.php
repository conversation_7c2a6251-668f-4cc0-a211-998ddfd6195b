<?php
header('Access-Control-Allow-Origin: http://localhost:3002'); header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE'); header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With'); header('Access-Control-Allow-Credentials: true'); header('Content-Type: application/json');
error_log("Request: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(200); exit(); }
function getDB() { $db = new PDO('sqlite:database/database.sqlite'); $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); $db->exec("CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT NOT NULL, email TEXT UNIQUE NOT NULL, password TEXT NOT NULL, role TEXT DEFAULT 'user', created_at DATETIME DEFAULT CURRENT_TIMESTAMP)"); return $db; }

session_start(); $method = $_SERVER['REQUEST_METHOD']; $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH); $path = preg_replace('/^.*\/simple-server\.php/', '', $path); if (empty($path)) { $path = '/'; } error_log("Parsed path: " . $path); $input = json_decode(file_get_contents('php://input'), true);

switch ($path) {
    case '/':
        echo json_encode(['message' => 'Graduate Tracker API', 'Laravel' => 'Simple PHP Server']);
        break;

    case '/sanctum/csrf-cookie': setcookie('XSRF-TOKEN', 'test-csrf-token', time() + 3600, '/', '', false, false); http_response_code(204); break;

    case '/register':
        if ($method === 'POST') {
            $name = $input['name'] ?? '';
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($name) || empty($email) || empty($password)) { http_response_code(422); echo json_encode(['errors' => ['All fields are required']]); break; }
            try { $db = getDB(); $checkStmt = $db->prepare("SELECT id FROM users WHERE email = ?"); $checkStmt->execute([$email]); if ($checkStmt->fetch()) { http_response_code(422); echo json_encode(['errors' => ['Email already exists']]); break; }
            $role = (strpos($email, 'admin') !== false) ? 'admin' : 'user'; $stmt = $db->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)"); $stmt->execute([$name, $email, $password, $role]); $userId = $db->lastInsertId();
            $newUser = ['id' => $userId, 'name' => $name, 'email' => $email, 'role' => $role]; $_SESSION['user'] = $newUser;
            http_response_code(201); echo json_encode(['message' => 'User registered successfully', 'user' => $newUser]); } catch (Exception $e) { http_response_code(500); echo json_encode(['errors' => ['Database error']]); }
        }
        break;

    case '/login':
        if ($method === 'POST') {
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($email) || empty($password)) { http_response_code(422); echo json_encode(['errors' => ['Email and password are required']]); break; }
            try { $db = getDB(); $stmt = $db->prepare("SELECT id, name, email, role FROM users WHERE email = ? AND password = ?"); $stmt->execute([$email, $password]); $userData = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$userData) { http_response_code(401); echo json_encode(['errors' => ['Invalid email or password']]); break; }
            $_SESSION['user'] = $userData;
            http_response_code(200); echo json_encode(['message' => 'Login successful', 'user' => $userData]); } catch (Exception $e) { http_response_code(500); echo json_encode(['errors' => ['Database error']]); }
        }
        break;
        
    case '/api/user':
        if ($method === 'GET') {
            if (isset($_SESSION['user'])) {
                echo json_encode($_SESSION['user']);
            } else {
                http_response_code(401);
                echo json_encode(['message' => 'Unauthenticated']);
            }
        }
        break;
        
    case '/logout':
        if ($method === 'POST') {
            session_destroy();
            http_response_code(204);
        }
        break;

    case '/api/users':
        if ($method === 'GET') {
            try { $db = getDB(); $stmt = $db->query("SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC"); $users = $stmt->fetchAll(PDO::FETCH_ASSOC); echo json_encode(['users' => $users]); } catch (Exception $e) { http_response_code(500); echo json_encode(['errors' => ['Database error']]); }
        }
        break;

    default:
        http_response_code(404);
        echo json_encode(['message' => 'Not found']);
        break;
}
?>
