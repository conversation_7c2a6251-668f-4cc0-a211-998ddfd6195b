{"packages": [{"name": "brick/math", "version": "0.12.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "f510c0a40911935b77b86859eb5223d58d660df1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/f510c0a40911935b77b86859eb5223d58d660df1", "reference": "f510c0a40911935b77b86859eb5223d58d660df1", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "5.16.0"}, "time": "2023-11-29T23:19:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "install-path": "../brick/math"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "time": "2024-02-09T16:56:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "install-path": "../carbonphp/carbon-doctrine-types"}, {"name": "doctrine/inflector", "version": "2.0.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "time": "2024-02-18T20:23:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "install-path": "../doctrine/inflector"}, {"name": "doctrine/lexer", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "time": "2024-02-05T11:56:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "install-path": "../doctrine/lexer"}, {"name": "egulias/email-validator", "version": "4.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ebaaf5be6c0286928352e054f2d5125608e5405e", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "time": "2023-10-06T06:47:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "install-path": "../egulias/email-validator"}, {"name": "illuminate/bus", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "9b9f7b4aefb4773772a827f9562336805926fe63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/9b9f7b4aefb4773772a827f9562336805926fe63", "reference": "9b9f7b4aefb4773772a827f9562336805926fe63", "shasum": ""}, "require": {"illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/pipeline": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "time": "2024-10-31T17:34:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/bus"}, {"name": "illuminate/collections", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "a01a9d0799700bf34ab3797988fdd5f420d42bfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/a01a9d0799700bf34ab3797988fdd5f420d42bfe", "reference": "a01a9d0799700bf34ab3797988fdd5f420d42bfe", "shasum": ""}, "require": {"illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "php": "^8.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^7.0)."}, "time": "2024-11-15T15:43:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/collections"}, {"name": "illuminate/conditionable", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/conditionable.git", "reference": "362dd761b9920367bca1427a902158225e9e3a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/362dd761b9920367bca1427a902158225e9e3a23", "reference": "362dd761b9920367bca1427a902158225e9e3a23", "shasum": ""}, "require": {"php": "^8.0.2"}, "time": "2024-06-28T20:10:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/conditionable"}, {"name": "illuminate/console", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/console.git", "reference": "c4c9e69e240601cca841ec327e8e804deb6b965e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/console/zipball/c4c9e69e240601cca841ec327e8e804deb6b965e", "reference": "c4c9e69e240601cca841ec327e8e804deb6b965e", "shasum": ""}, "require": {"ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "illuminate/view": "^11.0", "laravel/prompts": "^0.1.20|^0.2|^0.3", "nunomaduro/termwind": "^2.0", "php": "^8.2", "symfony/console": "^7.0", "symfony/polyfill-php83": "^1.28", "symfony/process": "^7.0"}, "suggest": {"dragonmantank/cron-expression": "Required to use scheduler (^3.3.2).", "ext-pcntl": "Required to use signal trapping.", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (^7.8).", "illuminate/bus": "Required to use the scheduled job dispatcher (^11.0).", "illuminate/container": "Required to use the scheduler (^11.0).", "illuminate/filesystem": "Required to use the generator command (^11.0).", "illuminate/queue": "Required to use closures for scheduled jobs (^11.0)."}, "time": "2024-11-15T15:27:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Console package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/console"}, {"name": "illuminate/container", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "6e31eb49e9c9e68356a55cd8f18fb8830b8158cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/6e31eb49e9c9e68356a55cd8f18fb8830b8158cd", "reference": "6e31eb49e9c9e68356a55cd8f18fb8830b8158cd", "shasum": ""}, "require": {"illuminate/contracts": "^11.0", "php": "^8.2", "psr/container": "^1.1.1|^2.0.1"}, "provide": {"psr/container-implementation": "1.1|2.0"}, "time": "2024-11-14T15:31:35+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/container"}, {"name": "illuminate/contracts", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "44c15aec6ea0d997e0885aa5b04876fe8a141433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/44c15aec6ea0d997e0885aa5b04876fe8a141433", "reference": "44c15aec6ea0d997e0885aa5b04876fe8a141433", "shasum": ""}, "require": {"php": "^8.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "time": "2024-11-15T15:40:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/contracts"}, {"name": "illuminate/events", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "af826028f0a4e65a331ceb9bebfa6a71467ba72c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/af826028f0a4e65a331ceb9bebfa6a71467ba72c", "reference": "af826028f0a4e65a331ceb9bebfa6a71467ba72c", "shasum": ""}, "require": {"illuminate/bus": "^11.0", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "time": "2024-10-31T17:34:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/events"}, {"name": "illuminate/filesystem", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "ce7013a350fb06bc65e8a2cf15fd2015f49e476d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/ce7013a350fb06bc65e8a2cf15fd2015f49e476d", "reference": "ce7013a350fb06bc65e8a2cf15fd2015f49e476d", "shasum": ""}, "require": {"illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2", "symfony/finder": "^7.0"}, "suggest": {"ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-hash": "Required to use the Filesystem class.", "illuminate/http": "Required for handling uploaded files (^7.0).", "league/flysystem": "Required to use the Flysystem local driver (^3.0.16).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^7.0).", "symfony/mime": "Required to enable support for guessing extensions (^7.0)."}, "time": "2024-09-22T15:10:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/filesystem"}, {"name": "illuminate/macroable", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed", "reference": "e1cb9e51b9ed5d3c9bc1ab431d0a52fe42a990ed", "shasum": ""}, "require": {"php": "^8.2"}, "time": "2024-06-28T20:10:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/macroable"}, {"name": "illuminate/pipeline", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "b359be74adc3ba4a637ca01c3645a26724a4c8a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/b359be74adc3ba4a637ca01c3645a26724a4c8a0", "reference": "b359be74adc3ba4a637ca01c3645a26724a4c8a0", "shasum": ""}, "require": {"illuminate/contracts": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "time": "2024-10-18T13:11:08+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/pipeline"}, {"name": "illuminate/support", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "213bc04ed2a75dac441e602df4568154c36a3670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/213bc04ed2a75dac441e602df4568154c36a3670", "reference": "213bc04ed2a75dac441e602df4568154c36a3670", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "nesbot/carbon": "^2.72.2|^3.0", "php": "^8.2", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"spatie/once": "*"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^11.0).", "laravel/serializable-closure": "Required to use the once function (^1.3).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the composer class (^7.0).", "symfony/uid": "Required to use Str::ulid() (^7.0).", "symfony/var-dumper": "Required to use the dd function (^7.0).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "time": "2024-11-14T16:30:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/support"}, {"name": "illuminate/translation", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/translation.git", "reference": "8ef07f0148bf48e4f342efdcd39869c24cb7c81f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/translation/zipball/8ef07f0148bf48e4f342efdcd39869c24cb7c81f", "reference": "8ef07f0148bf48e4f342efdcd39869c24cb7c81f", "shasum": ""}, "require": {"illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/filesystem": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "time": "2024-11-14T15:34:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Translation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/translation"}, {"name": "illuminate/validation", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/validation.git", "reference": "ac7cfbf48fdafc3b0a6ca13f0547d2bec83261ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/validation/zipball/ac7cfbf48fdafc3b0a6ca13f0547d2bec83261ed", "reference": "ac7cfbf48fdafc3b0a6ca13f0547d2bec83261ed", "shasum": ""}, "require": {"brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "egulias/email-validator": "^3.2.5|^4.0", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "illuminate/translation": "^11.0", "php": "^8.2", "symfony/http-foundation": "^7.0", "symfony/mime": "^7.0"}, "suggest": {"illuminate/database": "Required to use the database presence verifier (^11.0)."}, "time": "2024-11-08T03:50:36+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Validation package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/validation"}, {"name": "illuminate/view", "version": "v11.32.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/illuminate/view.git", "reference": "b0ca05925b882b7887f9c2591497a1b2835f9144"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/view/zipball/b0ca05925b882b7887f9c2591497a1b2835f9144", "reference": "b0ca05925b882b7887f9c2591497a1b2835f9144", "shasum": ""}, "require": {"ext-tokenizer": "*", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/events": "^11.0", "illuminate/filesystem": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "php": "^8.2"}, "time": "2024-10-24T14:26:35+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/view"}, {"name": "laravel/prompts", "version": "v0.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laravel/prompts.git", "reference": "0e0535747c6b8d6d10adca8b68293cf4517abb0f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/prompts/zipball/0e0535747c6b8d6d10adca8b68293cf4517abb0f", "reference": "0e0535747c6b8d6d10adca8b68293cf4517abb0f", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "ext-mbstring": "*", "php": "^8.1", "symfony/console": "^6.2|^7.0"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "require-dev": {"illuminate/collections": "^10.0|^11.0", "mockery/mockery": "^1.5", "pestphp/pest": "^2.3|^3.4", "phpstan/phpstan": "^1.11", "phpstan/phpstan-mockery": "^1.1"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "time": "2024-11-12T14:59:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "0.3.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Prompts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add beautiful and user-friendly forms to your command-line applications.", "support": {"issues": "https://github.com/laravel/prompts/issues", "source": "https://github.com/laravel/prompts/tree/v0.3.2"}, "install-path": "../laravel/prompts"}, {"name": "laravel/ui", "version": "v4.5.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laravel/ui.git", "reference": "c75396f63268c95b053c8e4814eb70e0875e9628"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/ui/zipball/c75396f63268c95b053c8e4814eb70e0875e9628", "reference": "c75396f63268c95b053c8e4814eb70e0875e9628", "shasum": ""}, "require": {"illuminate/console": "^9.21|^10.0|^11.0", "illuminate/filesystem": "^9.21|^10.0|^11.0", "illuminate/support": "^9.21|^10.0|^11.0", "illuminate/validation": "^9.21|^10.0|^11.0", "php": "^8.0", "symfony/console": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.35|^8.15|^9.0", "phpunit/phpunit": "^9.3|^10.4|^11.0"}, "time": "2024-05-08T18:07:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "laravel": {"providers": ["Laravel\\Ui\\UiServiceProvider"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Laravel\\Ui\\": "src/", "Illuminate\\Foundation\\Auth\\": "auth-backend/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel UI utilities and presets.", "keywords": ["laravel", "ui"], "support": {"source": "https://github.com/laravel/ui/tree/v4.5.2"}, "install-path": "../laravel/ui"}, {"name": "nesbot/carbon", "version": "3.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "e1268cdbc486d97ce23fef2c666dc3c6b6de9947"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/e1268cdbc486d97ce23fef2c666dc3c6b6de9947", "reference": "e1268cdbc486d97ce23fef2c666dc3c6b6de9947", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.57.2", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.11.2", "phpunit/phpunit": "^10.5.20", "squizlabs/php_codesniffer": "^3.9.0"}, "time": "2024-11-07T17:46:48+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev", "dev-2.x": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "nunomaduro/termwind", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "42c84e4e8090766bbd6445d06cd6e57650626ea3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/42c84e4e8090766bbd6445d06cd6e57650626ea3", "reference": "42c84e4e8090766bbd6445d06cd6e57650626ea3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.2", "symfony/console": "^7.1.5"}, "require-dev": {"illuminate/console": "^11.28.0", "laravel/pint": "^1.18.1", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0", "phpstan/phpstan": "^1.12.6", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^7.1.5", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "time": "2024-10-15T16:15:16+00:00", "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}, "branch-alias": {"dev-2.x": "2.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v2.2.0"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "install-path": "../nunomaduro/termwind"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "symfony/clock", "version": "v7.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "97bebc53548684c17ed696bc8af016880f0f098d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/97bebc53548684c17ed696bc8af016880f0f098d", "reference": "97bebc53548684c17ed696bc8af016880f0f098d", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/clock"}, {"name": "symfony/console", "version": "v7.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "ff04e5b5ba043d2badfb308197b9e6b42883fcd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/ff04e5b5ba043d2badfb308197b9e6b42883fcd5", "reference": "ff04e5b5ba043d2badfb308197b9e6b42883fcd5", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "time": "2024-11-06T14:23:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.1.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-04-18T09:32:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/finder", "version": "v7.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "2cb89664897be33f78c65d3d2845954c8d7a43b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/2cb89664897be33f78c65d3d2845954c8d7a43b8", "reference": "2cb89664897be33f78c65d3d2845954c8d7a43b8", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "time": "2024-10-01T08:31:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-foundation", "version": "v7.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "f4419ec69ccfc3f725a4de7c20e4e57626d10112"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/f4419ec69ccfc3f725a4de7c20e4e57626d10112", "reference": "f4419ec69ccfc3f725a4de7c20e4e57626d10112", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "time": "2024-11-09T09:16:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.1.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/mime", "version": "v7.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "caa1e521edb2650b8470918dfe51708c237f0598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/caa1e521edb2650b8470918dfe51708c237f0598", "reference": "caa1e521edb2650b8470918dfe51708c237f0598", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "time": "2024-10-25T15:11:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/mime"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php83"}, {"name": "symfony/process", "version": "v7.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "42783370fda6e538771f7c7a36e9fa2ee3a84892"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/42783370fda6e538771f7c7a36e9fa2ee3a84892", "reference": "42783370fda6e538771f7c7a36e9fa2ee3a84892", "shasum": ""}, "require": {"php": ">=8.2"}, "time": "2024-11-06T14:23:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.1.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/service-contracts", "version": "v3.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "time": "2024-04-18T09:32:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/string", "version": "v7.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "591ebd41565f356fcd8b090fe64dbb5878f50281"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/591ebd41565f356fcd8b090fe64dbb5878f50281", "reference": "591ebd41565f356fcd8b090fe64dbb5878f50281", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "time": "2024-11-13T13:31:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.1.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "symfony/translation", "version": "v7.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "b9f72ab14efdb6b772f85041fa12f820dee8d55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/b9f72ab14efdb6b772f85041fa12f820dee8d55f", "reference": "b9f72ab14efdb6b772f85041fa12f820dee8d55f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "time": "2024-09-28T12:35:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v3.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b9d2189887bb6b2e0367a9fc7136c5239ab9b05a", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-04-18T09:32:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "voku/portable-ascii", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2022-03-08T17:03:00+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "install-path": "../voku/portable-ascii"}], "dev": false, "dev-package-names": []}